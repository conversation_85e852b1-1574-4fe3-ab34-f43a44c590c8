"use client";

import { MessageCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useMediaQuery } from "@/hooks/use-media-query";

interface ChatToggleButtonProps {
  isOpen: boolean;
  onClick: () => void;
  className?: string;
  chatWidth?: number;
}

export function ChatToggleButton({ isOpen, onClick, className, chatWidth = 320 }: ChatToggleButtonProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  return (
    <Button
      variant={isOpen ? "default" : "outline"}
      size="sm"
      onClick={onClick}
      className={cn(
        "fixed z-50 h-10 w-10 p-0 text-muted-foreground hover:text-foreground touch-manipulation",
        isDesktop
          ? "top-[80vh] -translate-y-1/2" // Desktop: right side, 80% viewport height
          : "bottom-20 left-4", // Mobile: bottom left
        className
      )}
      style={isDesktop ? {
        right: isOpen ? `${chatWidth + 16}px` : '16px', // Dynamic positioning based on chat width
        transition: 'right 300ms cubic-bezier(0.4, 0, 0.2, 1)', // Smooth positioning transition
        pointerEvents: 'auto',
        touchAction: 'manipulation',
        WebkitTapHighlightColor: 'transparent'
      } : {
        pointerEvents: 'auto',
        touchAction: 'manipulation',
        WebkitTapHighlightColor: 'transparent'
      }}
      title={isOpen ? "Close chat" : "Open chat"}
      type="button"
      role="button"
      aria-label={isOpen ? "Close chat" : "Open chat"}
    >
      <MessageCircle className="h-4 w-4" />
    </Button>
  );
}
